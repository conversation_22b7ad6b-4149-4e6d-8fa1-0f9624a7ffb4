GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_record_query_trace (1.8.2)
      activerecord (>= 6.0.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (1.7.0)
      activerecord (>= 4.2)
    activerecord-postgres_enum (2.1.0)
      activerecord (>= 5.2)
      pg
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    after_commit_everywhere (1.4.0)
      activerecord (>= 4.2)
      activesupport
    ast (2.4.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.952.0)
    aws-sdk-core (3.201.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.156.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    bigdecimal (3.1.9)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    builder (3.3.0)
    byebug (11.1.3)
    cairo (1.17.13)
      native-package-installer (>= 1.0.3)
      pkg-config (>= 1.2.2)
      red-colors
    cairo-gobject (4.2.2)
      cairo (>= 1.16.2)
      glib2 (= 4.2.2)
    childprocess (5.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    crass (1.0.6)
    database_cleaner (2.0.2)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    declarative (0.0.20)
    diff-lcs (1.5.1)
    discard (1.3.0)
      activerecord (>= 4.2, < 8)
    dox (2.3.0)
      activesupport (>= 4.0)
      rspec-core
    ed25519 (1.3.0)
    elasticsearch (7.17.11)
      elasticsearch-api (= 7.17.11)
      elasticsearch-transport (= 7.17.11)
    elasticsearch-api (7.17.11)
      multi_json
    elasticsearch-model (7.2.1)
      activesupport (> 3)
      elasticsearch (~> 7)
      hashie
    elasticsearch-persistence (7.2.1)
      activemodel (> 4)
      activesupport (> 4)
      elasticsearch (~> 7)
      elasticsearch-model (= 7.2.1)
      hashie
    elasticsearch-rails (7.2.1)
    elasticsearch-transport (7.17.11)
      base64
      faraday (>= 1, < 3)
      multi_json
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    factory_bot (4.11.1)
      activesupport (>= 3.0.0)
    factory_bot_rails (4.11.1)
      factory_bot (~> 4.11.1)
      railties (>= 3.0.0)
    faker (3.4.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.12.0)
      faraday-net_http (>= 2.0, < 3.4)
      json
      logger
    faraday-httpclient (2.0.1)
      httpclient (>= 2.2)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (3.3.0)
      net-http
    ffi (1.17.0)
    fiddle (1.1.2)
    figaro (1.2.0)
      thor (>= 0.14.0, < 2)
    fugit (1.11.0)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    gems (1.2.0)
    gio2 (4.2.2)
      fiddle
      gobject-introspection (= 4.2.2)
    glib2 (4.2.2)
      native-package-installer (>= 1.0.3)
      pkg-config (>= 1.3.5)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gobject-introspection (4.2.2)
      glib2 (= 4.2.2)
    google-api-client (0.53.0)
      google-apis-core (~> 0.1)
      google-apis-generator (~> 0.1)
    google-apis-bigquery_v2 (0.92.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.15.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-discovery_v1 (0.17.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-generator (0.15.0)
      activesupport (>= 5.0)
      gems (~> 1.2)
      google-apis-core (>= 0.15.0, < 2.a)
      google-apis-discovery_v1 (~> 0.14)
      thor (>= 0.20, < 2.a)
    google-cloud-bigquery (1.52.1)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      google-apis-bigquery_v2 (~> 0.71)
      google-apis-core (~> 0.13)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.1.1)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-id-token (1.4.2)
      jwt (>= 1)
    google-protobuf (4.29.3)
      bigdecimal
      rake (>= 13)
    googleapis-common-protos-types (1.18.0)
      google-protobuf (>= 3.18, < 5.a)
    googleauth (1.11.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashie (5.0.0)
    honeybadger (4.12.2)
    httpclient (2.8.3)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    jmespath (1.6.2)
    json (2.7.2)
    jwt (2.9.3)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.6.1)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    lograge-sql (2.5.1)
      activerecord (>= 5, < 8.1)
      lograge (~> 0.11)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.24.1)
    mixpanel-ruby (2.3.0)
    mock_redis (0.44.0)
    msgpack (1.7.2)
    multi_json (1.15.0)
    multipart-post (2.4.1)
    native-package-installer (1.1.9)
    neighbor (0.5.0)
      activerecord (>= 7)
    net-http (0.4.1)
      uri
    net-imap (0.4.16)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.16.6)
      racc (~> 1.4)
    opentelemetry-api (1.4.0)
    opentelemetry-common (0.21.0)
      opentelemetry-api (~> 1.0)
    opentelemetry-exporter-otlp (0.29.1)
      google-protobuf (>= 3.18)
      googleapis-common-protos-types (~> 1.3)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-sdk (~> 1.2)
      opentelemetry-semantic_conventions
    opentelemetry-helpers-sql-obfuscation (0.3.0)
      opentelemetry-common (~> 0.21)
    opentelemetry-instrumentation-action_mailer (0.4.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-action_pack (0.12.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-rack (~> 0.21)
    opentelemetry-instrumentation-action_view (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_job (0.8.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_record (0.9.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_storage (0.1.0)
      opentelemetry-api (~> 1.4.0)
      opentelemetry-instrumentation-active_support (~> 0.7)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-active_support (0.8.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-aws_sdk (0.8.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-base (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-common (~> 0.21)
      opentelemetry-registry (~> 0.1)
    opentelemetry-instrumentation-concurrent_ruby (0.22.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-faraday (0.26.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-http_client (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-net_http (0.23.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-pg (0.30.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-helpers-sql-obfuscation
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-rack (0.26.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-rails (0.36.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-action_mailer (~> 0.4.0)
      opentelemetry-instrumentation-action_pack (~> 0.12.0)
      opentelemetry-instrumentation-action_view (~> 0.9.0)
      opentelemetry-instrumentation-active_job (~> 0.8.0)
      opentelemetry-instrumentation-active_record (~> 0.9.0)
      opentelemetry-instrumentation-active_storage (~> 0.1.0)
      opentelemetry-instrumentation-active_support (~> 0.8.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
      opentelemetry-instrumentation-concurrent_ruby (~> 0.22.0)
    opentelemetry-instrumentation-redis (0.26.1)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-instrumentation-sidekiq (0.26.0)
      opentelemetry-api (~> 1.0)
      opentelemetry-instrumentation-base (~> 0.23.0)
    opentelemetry-registry (0.3.1)
      opentelemetry-api (~> 1.1)
    opentelemetry-sdk (1.7.0)
      opentelemetry-api (~> 1.1)
      opentelemetry-common (~> 0.20)
      opentelemetry-registry (~> 0.2)
      opentelemetry-semantic_conventions
    opentelemetry-semantic_conventions (1.10.1)
      opentelemetry-api (~> 1.0)
    os (1.1.4)
    parallel (1.25.1)
    parser (3.3.3.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.6)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    pkg-config (1.5.6)
    poppler (4.2.2)
      cairo-gobject (= 4.2.2)
      gio2 (= 4.2.2)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (3.3.4)
    public_suffix (5.1.1)
    puma (5.6.9)
      nio4r (~> 2.0)
    qdrant-ruby (0.9.9)
      faraday (>= 2.0.1, < 3)
    raabro (1.4.0)
    racc (1.8.0)
    rack (2.2.9)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    red-colors (0.4.0)
      json
      matrix
    redis (4.8.1)
    redis-actionpack (5.4.0)
      actionpack (>= 5, < 8)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.10.0)
      redis (>= 4, < 6)
    regexp_parser (2.10.0)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    request_store-sidekiq (0.1.0)
      request_store (>= 1.3)
      sidekiq (>= 3.0)
    retriable (3.1.2)
    rexml (3.3.1)
      strscan
    ros-apartment (3.1.0)
      activerecord (>= 6.1.0, < 7.2)
      parallel (< 2.0)
      public_suffix (>= 2.0.5, < 6.0)
      rack (>= 1.3.6, < 4.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-json_expectations (2.2.0)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (4.0.2)
      actionpack (>= 4.2)
      activesupport (>= 4.2)
      railties (>= 4.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-sqlimit (0.0.6)
      activerecord (>= 4.2.0, < 8)
      rspec (~> 3.0)
    rspec-support (3.13.1)
    rubocop (1.69.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.36.2, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.37.0)
      parser (>= *******)
    rubocop-performance (1.21.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.25.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rspec (3.0.2)
      rubocop (~> 1.61)
    ruby-openai (7.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    seedbank (0.5.0)
      rake (>= 10.0)
    sentry-rails (4.8.1)
      railties (>= 5.0)
      sentry-ruby-core (~> 4.8.1)
    sentry-ruby (4.8.1)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      faraday (>= 1.0)
      sentry-ruby-core (= 4.8.1)
    sentry-ruby-core (4.8.1)
      concurrent-ruby
      faraday
    sentry-sidekiq (4.8.1)
      sentry-ruby-core (~> 4.8.1)
      sidekiq (>= 3.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    sidekiq-cron (1.5.1)
      fugit (~> 1)
      sidekiq (>= 4.2.1)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    spring (2.1.1)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    strscan (3.1.0)
    thor (1.3.1)
    timecop (0.9.10)
    timeout (0.4.1)
    trailblazer-option (0.1.2)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (3.1.3)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (0.13.1)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    zeitwerk (2.6.16)

PLATFORMS
  ruby

DEPENDENCIES
  active_record_query_trace
  activerecord-import
  activerecord-postgres_enum
  after_commit_everywhere
  aws-sdk-s3
  bcrypt (~> 3.1.7)
  bcrypt_pbkdf
  bootsnap (>= 1.4.2)
  byebug
  database_cleaner
  discard (~> 1.2)
  dox
  ed25519
  elasticsearch-model (~> 7.2, >= 7.2.1)
  elasticsearch-persistence (~> 7.2, >= 7.2.1)
  elasticsearch-rails (~> 7.2, >= 7.2.1)
  event_stream_parser (~> 1.0)
  factory_bot_rails (~> 4.0)
  faker
  faraday
  faraday-httpclient
  figaro
  google-api-client
  google-cloud-bigquery
  google-id-token
  honeybadger (~> 4.0)
  jwt
  kaminari
  letter_opener
  listen (~> 3.2)
  lograge
  lograge-sql (~> 2.5.1)
  mixpanel-ruby
  mock_redis
  neighbor
  opentelemetry-exporter-otlp
  opentelemetry-instrumentation-aws_sdk
  opentelemetry-instrumentation-faraday
  opentelemetry-instrumentation-http_client
  opentelemetry-instrumentation-net_http
  opentelemetry-instrumentation-pg
  opentelemetry-instrumentation-rack
  opentelemetry-instrumentation-rails
  opentelemetry-instrumentation-redis
  opentelemetry-instrumentation-sidekiq
  opentelemetry-sdk
  pg (~> 1.3)
  pg_search
  poppler
  pry-rails
  psych (< 4)
  puma (~> 5.6)
  qdrant-ruby
  rack-cors
  rails (~> *******)
  redis
  redis-rails
  request_store (~> 1.5)
  request_store-sidekiq (= 0.1.0)
  ros-apartment
  rspec-json_expectations
  rspec-rails (~> 4.0.2)
  rspec-sqlimit
  rubocop (~> 1.69.2)
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  ruby-openai (~> 7.1.0)
  seedbank
  sentry-rails (~> 4.8, >= 4.8.1)
  sentry-ruby (~> 4.8, >= 4.8.1)
  sentry-sidekiq (~> 4.8, >= 4.8.1)
  shoulda-matchers (~> 4.0)
  sidekiq (~> 6.5.10)
  sidekiq-cron (~> 1.5.0)
  spring
  spring-watcher-listen (~> 2.0.0)
  timecop
  tzinfo-data
  with_advisory_lock

RUBY VERSION
   ruby 3.3.2p78

BUNDLED WITH
   2.5.11
